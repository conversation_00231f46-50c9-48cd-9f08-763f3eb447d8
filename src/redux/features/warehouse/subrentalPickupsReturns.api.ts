import { UseToast } from '@/components/ui/toast/ToastContainer';
import { WAREHOUSE_SUBRENTAL_PICKUPS_RETURNS_API_ROUTES } from '@/constants/api-constants/warehouse-api.constants';
import { baseQueryWithReauth } from '@/services/api';
import { ApiResponseDto, PaginationFilterPayload } from '@/types/common.types';
import { WarehouseSubrentalPickupsReturnsListTypes } from '@/types/warehouse.types';
import { createApi } from '@reduxjs/toolkit/query/react';

// Define the API service using RTK Query
export const warehouseSubrentalPickupsReturnsApi = createApi({
  reducerPath: 'warehouseSubrentalPickupsReturnsApi',
  baseQuery: baseQueryWithReauth,
  tagTypes: ['subrentalPickupsReturns', 'subrentalPickupsReturnsColumns'],
  endpoints: (builder) => ({
    getSubrentalPickupsReturnsColumns: builder.query<ApiResponseDto<any>, void>(
      {
        query: () => WAREHOUSE_SUBRENTAL_PICKUPS_RETURNS_API_ROUTES.GET_COLUMN,
        providesTags: ['subrentalPickupsReturnsColumns'],
      }
    ),
    updateSubrentalPickupsReturnsColumns: builder.mutation<
      ApiResponseDto<any>,
      any
    >({
      query: (body) => ({
        url: WAREHOUSE_SUBRENTAL_PICKUPS_RETURNS_API_ROUTES.UPDATE_COLUMN,
        method: 'POST',
        body,
      }),
      transformResponse: (response) => {
        if (response?.message) {
          UseToast().success(response.message);
        }
        // Return the response as it is after processing it
        return response;
      },

      invalidatesTags: ['subrentalPickupsReturnsColumns'],
    }),

    downloadFile: builder.mutation<ApiResponseDto<any>, any>({
      query: (payload) => ({
        url: WAREHOUSE_SUBRENTAL_PICKUPS_RETURNS_API_ROUTES.DOWNLOAD_FILE,
        method: 'POST',
        body: payload,
      }),
    }),
    getListForPrint: builder.query<ApiResponseDto<any>, any>({
      query: () => ({
        url: WAREHOUSE_SUBRENTAL_PICKUPS_RETURNS_API_ROUTES.GET_LIST_PRINT,
        method: 'GET',
      }),
    }),

    subrentalPickupsReturnsData: builder.query<
      ApiResponseDto<WarehouseSubrentalPickupsReturnsListTypes[]>,
      PaginationFilterPayload
    >({
      query: (body) => ({
        url: WAREHOUSE_SUBRENTAL_PICKUPS_RETURNS_API_ROUTES.ALL,
        method: 'POST',
        body,
      }),
      providesTags: ['subrentalPickupsReturns'],
    }),

    // update status
    updateSubrentalPickupsReturnsStatus: builder.mutation<
      ApiResponseDto<any>,
      any
    >({
      query: (payload) => ({
        url: WAREHOUSE_SUBRENTAL_PICKUPS_RETURNS_API_ROUTES.UPDATE_STATUS,
        method: 'POST',
        body: payload,
      }),
      invalidatesTags: ['subrentalPickupsReturns'],
      transformResponse: (response) => {
        if (response?.message) {
          UseToast().success(response.message);
        }
        return response;
      },
    }),
  }),
});

export const {
  useGetSubrentalPickupsReturnsColumnsQuery,
  useUpdateSubrentalPickupsReturnsColumnsMutation,
  useDownloadFileMutation,
  useGetListForPrintQuery,
  useSubrentalPickupsReturnsDataQuery,
  useUpdateSubrentalPickupsReturnsStatusMutation,
} = warehouseSubrentalPickupsReturnsApi;
