import AppButton from '@/components/common/app-button';
import DataTable from '@/components/common/data-tables';
import DatePicker from '@/components/forms/date-picker';
import InputField from '@/components/forms/input-field';
import Labels from '@/components/forms/Label';
import NumberInputField from '@/components/forms/number-input-field';
import SelectWidget from '@/components/forms/select';
import SelectDropDown from '@/components/forms/select-dropdown';
import { SHIPPING_COMPAINES_API_ROUTES } from '@/constants/api-constants';
import { selectYesNo, SHIP_INFO_TYPE } from '@/constants/order-constants';
import {
  cn,
  DATE_FORMAT_YYYYMMDD,
  formatDate,
  generateLabelValuePairs,
  getQueryParam,
} from '@/lib/utils';
import { useGetAllMutation } from '@/redux/features/common-api/common.api';
import {
  useAddReturnBoxMutation,
  useAddShippingBoxMutation,
  useGetAdditionalInfoShippingInfoBoxQuery,
  useGetAdditionalInfoShippingInfoItemQuery,
  useGetStatusForShippingInfoQuery,
  useGetTrackShippmentMutation,
  useMissingEquMutation,
  useSaveAdditionalInfoShippingInfoBoxMutation,
  useSaveAdditionalInfoShippingInfoItemMutation,
  useSplitRowInItemMutation,
} from '@/redux/features/orders/additional-info.api';
import {
  ShipBoxFormTypes,
  ShipInfoFormTypes,
  ShipItemDetails,
  ShipmentBoxInfo,
  STATUS_SHIP_INFO,
  TrackEquipmentStatus,
} from '@/types/order.types';
import { ColumnDef, RowSelectionState } from '@tanstack/react-table';
import {
  Box,
  Earth,
  EarthLock,
  File,
  Package,
  Save,
  Split,
} from 'lucide-react';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useFieldArray, useForm } from 'react-hook-form';

import { BoxType } from '@/components/modules/orders/constants';
import { TEXT_VALIDATION_RULE } from '@/constants/validation-constants';
import dayjs from 'dayjs';
import FutureInvoiceWarning from './dialogs/FutureInvoiceWarning';
import MissingEquipmentDialog from './dialogs/MissingEquipmentDialog';
import PrintBoxDialog from './dialogs/PrintBoxDialog';
import TrackingWarningDialog from './dialogs/TrackingWarningDialog';

interface ShippingInfoProps {
  toggle: (action?: string, orderId?: number | null) => void;
  shippingManager: boolean;
  shippingData: any;
}

const Shipping = ({ shippingManager, shippingData }: ShippingInfoProps) => {
  const id = getQueryParam('orderId') as string;
  const [isBoxDialogOpen, setIsBoxDialogOpen] = useState(false);
  //   const orderForm = useFormContext<OrderInformationTypes>();

  const [isLoadingTrackShipment, setIsLoadingTrackShipment] =
    useState<boolean>(false);
  const [isLoadingTrackReturn, setIsLoadingTrackReturn] =
    useState<boolean>(false);
  const [showFutureInvoiceWarning, setShowFutureInvoiceWarning] =
    useState(false);

  const { data: getShippingInfoItem, isLoading: isLoaderShippingInfoItem } =
    useGetAdditionalInfoShippingInfoItemQuery(id, {
      skip: !id,
      refetchOnMountOrArgChange: true,
    });

  const { data: getShippingInfoBox, isLoading: isLoaderShippingInfoBox } =
    useGetAdditionalInfoShippingInfoBoxQuery(id, {
      skip: !id,
    });
  const boxForm = useForm<ShipBoxFormTypes>({
    defaultValues: { box: [] }, // Prevent uncontrolled-to-controlled warning
  });

  const infoForm = useForm<ShipInfoFormTypes>({
    defaultValues: { info: [], shipStatus: '' }, // Prevent uncontrolled-to-controlled warning
  });

  const { fields: itemFields, append: appendItemRow } = useFieldArray({
    control: infoForm.control,
    name: 'info',
    keyName: 'fieldId',
  });

  const { fields: boxFields, append: appendBoxRow } = useFieldArray({
    control: boxForm.control,
    name: 'box',
    keyName: 'fieldId',
  });

  // Memoized default values when data is available
  const defaultBoxValues = useMemo(() => {
    const boxArray = Array.isArray(getShippingInfoBox?.data)
      ? getShippingInfoBox.data.map((box) => ({
          ...box,
          printLabel: String(box.printLabel),
        }))
      : [];

    return {
      box: boxArray,
    };
  }, [getShippingInfoBox?.data]);

  // Memoized default values when data is available
  const defaultInfoValues = useMemo(() => {
    const items = Array.isArray(getShippingInfoItem?.data?.shipItems)
      ? getShippingInfoItem.data.shipItems.map((item: any) => ({
          ...item,
          shipBoxNo: item.shipBoxNo === 0 ? '' : item.shipBoxNo?.toString(),
          returnBoxNo:
            item.returnBoxNo === 0 ? '' : item.returnBoxNo?.toString(),
        }))
      : [];

    return {
      info: items,
      shipStatus: getShippingInfoItem?.data?.shipStatus ?? 'NOT_SHIPPED',
      isCreatingME: getShippingInfoItem?.data?.isCreatingME ?? false,
    };
  }, [getShippingInfoItem?.data]);

  useEffect(() => {
    if (getShippingInfoItem?.data) {
      infoForm.reset(defaultInfoValues);
    }
  }, [defaultInfoValues, getShippingInfoItem?.data, infoForm]);

  useEffect(() => {
    if (getShippingInfoBox?.data) {
      boxForm.reset(defaultBoxValues);
    }
  }, [defaultBoxValues, getShippingInfoBox?.data, boxForm]);

  // State management
  const [selectedBoxRows, setSelectedBoxRows] = useState<RowSelectionState>({});
  const [selectedItemRows, setSelectedItemRows] = useState<RowSelectionState>(
    {}
  );
  const [shippingBoxList, setShippingBoxList] = useState<
    { label: string; value: string }[]
  >([]);
  const [returnedBoxList, setReturnedBoxList] = useState<
    { label: string; value: string }[]
  >([]);
  const [trackEquipmentStatus, setTrackEquipmentStatus] =
    useState<TrackEquipmentStatus>({
      shipping: false,
      return: false,
    });
  const [meDisale, setMeDisale] = useState<boolean>(false);
  // Update your state definition to include validation messages
  const [quantityValidation, setQuantityValidation] = useState({
    isValidShip: true,
    isValidReturn: true,
  });

  const [openMeDialog, setOpenMeDialog] = useState<boolean>(false);
  const [openTrackDialog, setOpenTrackDialog] = useState<boolean>(false);

  const onScrollRefItemData = useRef<HTMLTableElement>(null);
  const onScrollRefBoxData = useRef<HTMLTableElement>(null);

  const [splitRow] = useSplitRowInItemMutation();

  const [addShippingBox, { isLoading: isLoadingAddShippingBox }] =
    useAddShippingBoxMutation();

  const [addReturnBox, { isLoading: isLoadingAddReturnBox }] =
    useAddReturnBoxMutation();

  const [saveShippingInfoItem, { isLoading: isLoadingShippingInfoItem }] =
    useSaveAdditionalInfoShippingInfoItemMutation();

  const [saveShippingInfoBox, { isLoading: isLoadingShippingInfoBox }] =
    useSaveAdditionalInfoShippingInfoBoxMutation();

  const [getTrack] = useGetTrackShippmentMutation();

  const [createMissingEqu, { isLoading: missingequLoading }] =
    useMissingEquMutation();

  const [
    getShippingDetails,
    { data: shippingDetailsList, isLoading: isShippingCompanyTypeLoading },
  ] = useGetAllMutation();

  const { data: shipReturnStatusList } = useGetStatusForShippingInfoQuery({
    name: 'ShipStatus',
  });

  const statusList = shipReturnStatusList?.data;
  const isCreatingME = infoForm.watch('isCreatingME');

  useEffect(() => {
    if (onScrollRefItemData.current) {
      onScrollRefItemData.current.scrollTop =
        onScrollRefItemData.current.scrollHeight;
    }
  }, [itemFields]);

  useEffect(() => {
    if (onScrollRefBoxData.current) {
      onScrollRefBoxData.current.scrollTop =
        onScrollRefBoxData.current.scrollHeight;
    }
  }, [boxFields]);

  // Fetch shipping companies on initial load
  useEffect(() => {
    const fetchShippingCompanies = async () => {
      try {
        const payload = {
          pageNumber: 0,
          pageSize: 0,
          sortBy: '',
          sortAscending: true,
          filters: [],
        };
        await getShippingDetails({
          url: SHIPPING_COMPAINES_API_ROUTES.ALL,
          body: payload,
        });
      } catch (error) {
        return error;
      }
    };

    fetchShippingCompanies();
  }, [getShippingDetails]);

  // Process shipping companies list
  const uniqueCompanies = useMemo(() => {
    return generateLabelValuePairs({
      data: shippingDetailsList?.data || [],
      labelKey: 'name',
      valueKey: 'id',
    });
  }, [shippingDetailsList?.data]);

  // Update box lists when box data changes
  useEffect(() => {
    if (boxFields.length > 0) {
      // Create box option lists for dropdowns
      const updatedShippingBoxList = boxFields
        ?.filter((box) => box?.boxType === SHIP_INFO_TYPE?.SHIPPING)
        ?.map((box) => ({
          label: String(box?.boxNo || ''),
          value: String(box?.boxNo || ''),
        }))
        ?.sort((a, b) => Number(a.label) - Number(b.label));

      const updatedReturnBoxList = boxFields
        ?.filter((box) => box?.boxType === SHIP_INFO_TYPE?.RETURN)
        ?.map((box) => ({
          label: String(box?.boxNo || ''),
          value: String(box?.boxNo || ''),
        }))
        ?.sort((a, b) => Number(a.label) - Number(b.label));

      setShippingBoxList(updatedShippingBoxList);
      setReturnedBoxList(updatedReturnBoxList);
    }
  }, [boxFields]);

  // Handle split row action
  const handleSplitRow = useCallback(
    async (itemId: number) => {
      try {
        const result = await splitRow({ orderId: id, itemId }).unwrap();
        const newItems = Array.isArray(result.data)
          ? result.data
          : [result.data];
        appendItemRow([...newItems]);
      } catch (error) {
        return error;
      }
    },
    [splitRow, id, appendItemRow]
  );

  // Handle adding new box row
  const addNewRow = useCallback(
    async (boxType: string) => {
      try {
        if (boxType === SHIP_INFO_TYPE.SHIPPING) {
          try {
            const newBox = await addShippingBox({ orderId: id }).unwrap();
            appendBoxRow({
              id: newBox?.id,
              boxNo: newBox?.boxNo?.toString() || '',
              boxType: newBox?.boxType || SHIP_INFO_TYPE.SHIPPING,
              itemsWeight: newBox?.itemsWeight?.toString() || '0',
              boxWeight: newBox?.boxWeight?.toString() || '0',
              shipCompanyId: newBox?.shipCompanyId || 0,
              date: newBox?.date || null,
              printLabel: String(newBox?.printLabel) || 'false',
              trackingNo: newBox?.trackingNo || '',
              usNo: newBox?.usNo || '',
              packer: newBox?.packer || '',
              listShipCharg: newBox?.listShipCharg?.toString() || '0.00',
              actualShipCharg: newBox?.actualShipCharg?.toString() || '0.00',
              isDeleted: newBox?.isDeleted || false,
              orderId: newBox?.orderId || Number(id),
              shipCompany: newBox?.shipCompany || 1,
            }),
              boxForm.trigger('box');
          } catch (error) {}
        } else {
          try {
            const newBox = await addReturnBox({ orderId: id }).unwrap();
            appendBoxRow({
              id: newBox?.id,
              boxNo: newBox?.boxNo?.toString() || '',
              boxType: newBox?.boxType || SHIP_INFO_TYPE.SHIPPING,
              itemsWeight: newBox?.itemsWeight?.toString() || '',
              boxWeight: newBox?.boxWeight?.toString() || '',
              shipCompanyId: newBox?.shipCompanyId || 0,
              date: newBox?.date || null,
              printLabel: String(newBox?.printLabel) || 'false',
              trackingNo: newBox?.trackingNo || '',
              usNo: newBox?.usNo || '',
              packer: newBox?.packer || '',
              listShipCharg: newBox?.listShipCharg?.toString() || '0.00',
              actualShipCharg: newBox?.actualShipCharg?.toString() || '0.00',
              isDeleted: newBox?.isDeleted || false,
              orderId: newBox?.orderId || Number(id),
              shipCompany: newBox?.shipCompany || 1,
            }),
              boxForm.trigger('box');
          } catch (error) {}
        }
      } catch (error) {
        return error;
      }
    },
    [addShippingBox, id, appendBoxRow, boxForm, addReturnBox]
  );

  // Convert row selection state to array of selected indices
  const getSelectedIds = useCallback((selectionState: RowSelectionState) => {
    if (!selectionState || Object.keys(selectionState).length === 0) {
      return [];
    }

    // Get selected keys (IDs)
    return Object.keys(selectionState).filter(
      (key) => selectionState[key] === true
    );
  }, []);

  const getInfoAfterItemRowSelected = useCallback(() => {
    const formValues = infoForm.getValues();

    const selectedItemIds = getSelectedIds(selectedItemRows);

    const updatedItems = itemFields.map((item, index) => {
      const rowValues = formValues.info?.[index];
      if (!rowValues) return item;

      return {
        ...item,
        qtyShipped: Number(rowValues.qtyShipped) || 0,
        shipBoxNo: rowValues.shipBoxNo ?? null,
        qtyReturn: Number(rowValues.qtyReturn) || 0,
        returnBoxNo: rowValues.returnBoxNo ?? null,
        qtyDamaged: Number(rowValues.qtyDamaged) || 0,
        details: rowValues.details ?? '',
      };
    });

    const selectedItems = updatedItems.filter((item) =>
      selectedItemIds.includes(String(item.id))
    );

    return selectedItems;
  }, [infoForm, getSelectedIds, itemFields, selectedItemRows]);

  const getInfoAfterRowSelected = useCallback(() => {
    const formValues = boxForm.getValues();

    const selectedBoxIds = getSelectedIds(selectedBoxRows);

    const updatedBoxes = boxFields.map((box, index) => {
      const rowValues = formValues.box?.[index];
      if (!rowValues) return box;

      return {
        ...box,
        id: rowValues.id,
        orderId: rowValues.orderId,
        boxWeight: rowValues.boxWeight,
        shipCompanyId: rowValues.shipCompanyId,
        date: rowValues.date,
        printLabel: Boolean(rowValues.printLabel),
        trackingNo: rowValues.trackingNo,
        usNo: rowValues.usNo,
        packer: rowValues.packer,
        listShipCharg: rowValues.listShipCharg,
        actualShipCharg: rowValues.actualShipCharg,
      };
    });

    const selectedBoxes = updatedBoxes.filter((box) =>
      selectedBoxIds.includes(String(box.id))
    );

    return selectedBoxes;
  }, [boxFields, boxForm, getSelectedIds, selectedBoxRows]);

  // Define columns for item table & status of the item
  const updateShipStatus = useCallback(
    (row: any) => {
      const formValues = infoForm.getValues();
      const itemRows = formValues.info || [];
      const orderItemId = row?.original?.orderItemId;

      if (!orderItemId) return;

      let orderQtyAll = 0;
      let totalShippedAll = 0;
      let totalReturnedAll = 0;

      itemRows.forEach((row) => {
        orderQtyAll += Number(row.qtyOrdered) || 0;
        totalShippedAll += Number(row.qtyShipped) || 0;
        totalReturnedAll += Number(row.qtyReturn) || 0;
      });

      const relatedRows = itemRows.filter(
        (row) => row.orderItemId == orderItemId
      );
      if (relatedRows.length === 0) return;

      let orderQty = 0;
      let totalShipped = 0;
      let totalReturned = 0;

      const mainRow = relatedRows.find((row) => row.splitNo == 0);
      if (!mainRow) return;

      const mainRowIndex = itemRows.findIndex((row) => row.id === mainRow.id);
      if (mainRowIndex === -1) return;

      relatedRows.forEach((row) => {
        orderQty += Number(row.qtyOrdered) || 0;
        totalShipped += Number(row.qtyShipped) || 0;
        totalReturned += Number(row.qtyReturn) || 0;
      });

      const remainingQtyShip = orderQty - totalShipped;
      const remainingQtyReturn = orderQty - totalReturned;

      if (quantityValidation.isValidShip) {
        infoForm.setValue(
          `info.${mainRowIndex}.qtyTobeShipped`,
          remainingQtyShip
        );
      }

      if (quantityValidation.isValidReturn) {
        infoForm.setValue(
          `info.${mainRowIndex}.qtyTobeReturned`,
          remainingQtyReturn
        );
      }

      if (
        !quantityValidation.isValidShip ||
        !quantityValidation.isValidReturn
      ) {
        infoForm.trigger(`info`);
      }

      let newStatus = formValues.shipStatus;
      const existingStatus = getShippingInfoItem?.data?.shipStatus;

      if (existingStatus === STATUS_SHIP_INFO.SHIPPED) {
        if (totalReturnedAll === 0) {
          newStatus = STATUS_SHIP_INFO.NOT_RETURN;
        } else if (totalReturnedAll === orderQtyAll) {
          newStatus = STATUS_SHIP_INFO.RETURNED;
        } else {
          newStatus = STATUS_SHIP_INFO.PARTIALLY_RETURN;
        }
      } else {
        if (totalShippedAll === 0) {
          newStatus = STATUS_SHIP_INFO.NOT_SHIPPED;
        } else if (totalShippedAll === orderQtyAll) {
          newStatus = STATUS_SHIP_INFO.SHIPPED;
        } else {
          newStatus = STATUS_SHIP_INFO.PARTIALLY_SHIPPED;
        }
      }

      if (newStatus !== formValues.shipStatus) {
        infoForm.setValue('shipStatus', newStatus);
      }
      setTimeout(() => {
        infoForm.trigger();
      }, 0);
    },
    [
      infoForm,
      getShippingInfoItem?.data?.shipStatus,
      quantityValidation.isValidReturn,
      quantityValidation.isValidShip,
    ]
  );

  const onOpenChange = useCallback(() => {
    setOpenMeDialog((prevState) => !prevState);
  }, [setOpenMeDialog]);

  const onOpenTrackChange = useCallback(() => {
    setOpenTrackDialog((prevState) => !prevState);
  }, [setOpenTrackDialog]);

  const handleCreateMissingOrder = async () => {
    // const status = orderForm.watch('status');
    // const dateOfUseFrom = orderForm.watch('dateOfUseFrom');

    const isBefore = dayjs(shippingData?.dateOfUse).isBefore(dayjs(), 'day');

    if (shippingData?.status !== 'INVOICED' || !isBefore) {
      onOpenChange();
      setShowFutureInvoiceWarning((prev) => !prev);
      return;
    }
    try {
      const response = await createMissingEqu(id);
      if (response?.data?.createdOrderId) {
        onOpenChange();
      }
    } catch (error) {
      return error;
    }
  };

  const columnsDetailsInfo: ColumnDef<ShipItemDetails>[] = useMemo(() => {
    return [
      {
        accessorKey: 'item',
        header: 'Item Description',
        size: 200,
      },
      {
        accessorKey: 'qtyOrdered',
        header: 'Qty Ordered',
        size: 130,
      },
      {
        accessorKey: 'qtyShipped',
        header: 'Qty Shipped',
        size: 130,
        cell: ({ row }) => {
          const formValues = infoForm.getValues();
          const currentRow = row.original;
          const itemRows = formValues.info || [];

          // Find the main row (splitNo == 0) for the same orderItemId
          const mainRow = itemRows.find(
            (r: any) =>
              r.orderItemId === currentRow.orderItemId &&
              Number(r.splitNo) === 0
          );

          // Get qtyTobeShipped from the main row
          const maxValue =
            mainRow && typeof mainRow.qtyTobeShipped === 'number'
              ? mainRow.qtyTobeShipped
              : undefined;
          return (
            <div className="flex flex-col">
              <NumberInputField
                name={`info.${row.index}.qtyShipped`}
                form={infoForm}
                placeholder="Qty Shipped"
                maxLength={5}
                maxValue={maxValue}
                className={cn('max-w-[130px] h-8')}
                onBlur={() => updateShipStatus(row)}
                disabled={isCreatingME}
              />
            </div>
          );
        },
      },
      {
        accessorKey: 'shipBoxNo',
        header: 'Box #',
        size: 150,
        cell: ({ row }) => {
          const qtyShipped = infoForm.watch(`info.${row.index}.qtyShipped`);
          const validation = Number(qtyShipped) > 0 ? TEXT_VALIDATION_RULE : {};
          return (
            <SelectDropDown
              name={`info.${row.index}.shipBoxNo`}
              form={infoForm}
              label=""
              optionsList={shippingBoxList ?? []}
              className="max-w-[250px] bg-white h-8"
              allowClear={false}
              validation={validation}
              disabled={isCreatingME}
            />
          );
        },
      },
      {
        accessorKey: 'qtyTobeShipped',
        header: 'Qty To Be Shipped',
        size: 180,
        cell: ({ row }) => {
          const value = infoForm.watch(`info.${row.index}.qtyTobeShipped`);

          return (
            <div className="px-2 py-1">
              {typeof value === 'number' ? value : value || 0}
            </div>
          );
        },
      },
      {
        accessorKey: 'separator',
        header: '',
        size: 60,
      },
      {
        accessorKey: 'qtyReturn',
        header: 'Qty Returned',
        size: 150,
        cell: ({ row }) => {
          const formValues = infoForm.getValues();
          const currentRow = row.original;
          const itemRows = formValues.info || [];

          // Find the main row (splitNo == 0) for the same orderItemId
          const mainRow = itemRows.find(
            (r: any) =>
              r.orderItemId === currentRow.orderItemId &&
              Number(r.splitNo) === 0
          );

          // Get qtyTobeReturned from the main row
          const maxValue =
            mainRow && typeof mainRow.qtyTobeReturned === 'number'
              ? mainRow.qtyTobeReturned
              : undefined;
          return (
            <div className="flex flex-col">
              <NumberInputField
                name={`info.${row.index}.qtyReturn`}
                form={infoForm}
                placeholder="Qty Returned"
                maxLength={5}
                maxValue={maxValue}
                className={cn('h-8')}
                onBlur={() => updateShipStatus(row)}
                disabled={isCreatingME}
              />
            </div>
          );
        },
      },
      {
        accessorKey: 'returnBoxNo',
        header: 'Box #',
        size: 150,
        cell: ({ row }) => {
          const qtyReturn = infoForm.watch(`info.${row.index}.qtyReturn`);
          const validation = Number(qtyReturn) > 0 ? TEXT_VALIDATION_RULE : {};
          return (
            <SelectDropDown
              name={`info.${row.index}.returnBoxNo`}
              form={infoForm}
              label=""
              optionsList={returnedBoxList ?? []}
              className="max-w-[250px] bg-white h-8"
              allowClear={false}
              validation={validation}
              disabled={isCreatingME}
            />
          );
        },
      },
      {
        accessorKey: 'qtyTobeReturned',
        header: 'Qty To Be Returned',
        size: 190,
        cell: ({ row }) => {
          const value = infoForm.watch(`info.${row.index}.qtyTobeReturned`);

          return (
            <div className="px-2 py-1">
              {typeof value === 'number' ? value : value || 0}
            </div>
          );
        },
      },
      {
        accessorKey: 'qtyDamaged',
        header: 'Damaged Qty',
        size: 170,
        cell: ({ row }) => (
          <NumberInputField
            name={`info.${row.index}.qtyDamaged`}
            form={infoForm}
            placeholder="Damaged Qty"
            maxLength={5}
            pClassName="p-1"
            className="max-w-[170px] h-8"
            disabled={isCreatingME}
          />
        ),
      },
      {
        accessorKey: 'details',
        header: 'Damaged Details',
        size: 270,
        cell: ({ row }) => (
          <InputField
            name={`info.${row.index}.details`}
            form={infoForm}
            placeholder="Damaged Details"
            pClassName="p-1"
            className="max-w-[270px] h-8"
            disabled={isCreatingME}
          />
        ),
      },
      {
        id: 'action',
        header: 'Actions',
        size: 80,
        cell: ({ row }) => {
          const value = row.original?.orderItemId;
          const qtyOrdered = infoForm.watch(`info.${row.index}.qtyOrdered`);
          return (
            <AppButton
              label={'Split Item'}
              variant="neutral"
              icon={Split}
              iconClassName="w-4 h-4"
              onClick={() => handleSplitRow(value)}
              disabled={qtyOrdered < 2}
            />
          );
        },
      },
    ];
  }, [
    handleSplitRow,
    infoForm,
    isCreatingME,
    returnedBoxList,
    shippingBoxList,
    updateShipStatus,
  ]);

  // Define columns for box table
  const columnsDetailsBox: ColumnDef<ShipmentBoxInfo>[] = useMemo(() => {
    return [
      {
        accessorKey: 'boxNo',
        header: 'Box #',
        size: 80,
      },
      {
        accessorKey: 'boxType',
        header: 'Type',
        size: 80,
      },
      {
        accessorKey: 'itemsWeight',
        header: 'Est. Weight',
        size: 150,
      },
      {
        accessorKey: 'boxWeight',
        header: 'Act. Weight',
        size: 150,
        cell: ({ row }) => (
          <NumberInputField
            name={`box.${row.index}.boxWeight`}
            form={boxForm}
            maxLength={6}
            pClassName="p-1"
            className="max-w-[170px] h-8"
            disabled={isCreatingME}
          />
        ),
      },
      {
        accessorKey: 'shipCompanyId',
        header: 'Shipping Company',
        size: 180,
        cell: ({ row }) => (
          <SelectDropDown
            name={`box.${row.index}.shipCompanyId`}
            form={boxForm}
            label=""
            optionsList={uniqueCompanies ?? []}
            className="max-w-[250px] max-h-[31px]"
            isLoading={isShippingCompanyTypeLoading}
            disabled={isCreatingME}
          />
        ),
      },
      {
        accessorKey: 'date',
        header: 'Ship/Return Date',
        size: 170,
        cell: ({ row }) => (
          <DatePicker
            name={`box.${row.index}.date`}
            form={boxForm}
            label=""
            className="max-w-[250px] h-8"
            enableInput
            disabled={isCreatingME}
          />
        ),
      },
      {
        accessorKey: 'printLabel',
        header: 'Print Label',
        size: 150,
        cell: ({ row }) => (
          <SelectDropDown
            name={`box.${row.index}.printLabel`}
            form={boxForm}
            label=""
            optionsList={selectYesNo ?? []}
            className="max-w-[250px] max-h-[31px]"
            onChange={(value) => {
              boxForm.setValue(`box.${row.index}.printLabel`, value);
            }}
            disabled={isCreatingME}
          />
        ),
      },
      {
        accessorKey: 'trackingNo',
        header: 'Tracking #',
        size: 180,
        cell: ({ row }) => (
          <InputField
            name={`box.${row.index}.trackingNo`}
            form={boxForm}
            pClassName="p-1"
            className="max-w-[270px] h-8"
            maxLength={20}
            disabled={isCreatingME}
          />
        ),
      },
      {
        accessorKey: 'usNo',
        header: 'US #',
        size: 150,
        cell: ({ row }) => (
          <InputField
            name={`box.${row.index}.usNo`}
            form={boxForm}
            pClassName="p-1"
            className="max-w-[270px] h-8"
            maxLength={20}
            disabled={isCreatingME}
          />
        ),
      },
      {
        accessorKey: 'packer',
        header: 'Packer',
        size: 180,
        cell: ({ row }) => (
          <InputField
            name={`box.${row.index}.packer`}
            form={boxForm}
            pClassName="p-1"
            className="max-w-[270px] h-8"
            maxLength={35}
            disabled={isCreatingME}
          />
        ),
      },
      {
        accessorKey: 'listShipCharg',
        header: 'List Shipping Chg',
        size: 180,
        cell: ({ row }) => (
          <NumberInputField
            name={`box.${row.index}.listShipCharg`}
            form={boxForm}
            pClassName="p-1"
            className="max-w-[270px] h-8"
            prefix="$"
            fixedDecimalScale
            maxLength={10}
            disabled={isCreatingME}
          />
        ),
      },
      {
        accessorKey: 'actualShipCharg',
        header: 'Act. Shipping Chg',
        size: 180,
        cell: ({ row }) => (
          <NumberInputField
            name={`box.${row.index}.actualShipCharg`}
            form={boxForm}
            pClassName="p-1"
            className="max-w-[270px] h-8"
            prefix="$"
            fixedDecimalScale
            maxLength={10}
            disabled={isCreatingME}
          />
        ),
      },
    ];
  }, [boxForm, isCreatingME, isShippingCompanyTypeLoading, uniqueCompanies]);

  // Handle status change
  const handleStatusChange = useCallback(
    (value: string) => {
      infoForm.setValue('shipStatus', value);
    },
    [infoForm]
  );

  // Track shipment
  const trackShipment = useCallback(
    async (type: 'shipment' | 'return') => {
      try {
        if (getInfoAfterRowSelected().length === 0) return;

        const orderId = getInfoAfterRowSelected()[0]?.orderId;
        const pkId = getInfoAfterRowSelected()[0]?.id;
        const trackingNo = getInfoAfterRowSelected()[0]?.trackingNo;

        if (!orderId || !pkId) return;

        if (!trackingNo) {
          onOpenTrackChange();
        } else {
          if (type === 'shipment') setIsLoadingTrackShipment(true);
          else setIsLoadingTrackReturn(true);

          const response = await getTrack({
            orderId,
            pkId,
          }).unwrap();

          if (response?.data?.redirectUrl) {
            window.open(response.data.redirectUrl, '_blank');
          }
        }
      } catch (error) {
        return error;
      } finally {
        setIsLoadingTrackShipment(false);
        setIsLoadingTrackReturn(false);
      }
    },
    [getInfoAfterRowSelected, getTrack, onOpenTrackChange]
  );

  // Handle save changes
  const handleSaveItem = useCallback(async () => {
    try {
      if (shippingManager) {
        const allItemRows = infoForm.getValues('info');

        if (!allItemRows?.length) return;

        // Map form values to the format expected by the API
        const formattedItemRows = allItemRows.map((row) => ({
          id: row.id ? Number(row.id) : null,
          orderId: row.orderId ? Number(row.orderId) : Number(id),
          qtyShipped: row.qtyShipped ? Number(row.qtyShipped) : 0,
          shipBoxNo: row.shipBoxNo ? Number(row.shipBoxNo) : 0,
          qtyReturn: row.qtyReturn ? Number(row.qtyReturn) : 0,
          returnBoxNo: row.returnBoxNo ? Number(row.returnBoxNo) : 0,
          qtyDamaged: row.qtyDamaged ? Number(row.qtyDamaged) : 0,
          details: row.details || '',
        }));

        await saveShippingInfoItem({
          orderId: id,
          body: formattedItemRows,
        }).unwrap();
      }
    } catch (error) {
      return error;
    }
  }, [shippingManager, infoForm, id, saveShippingInfoItem]);

  const handleSaveBox = useCallback(async () => {
    try {
      const allBoxRows = boxForm.getValues('box');
      const formattedBoxes = allBoxRows.map((box) => ({
        id: box.id,
        shipCompanyId: Number(box.shipCompanyId),
        orderId: box.orderId,
        itemsWeight: Number(box.itemsWeight) ?? 0,
        boxWeight: Number(box.boxWeight) ?? 0,
        date: box.date ? formatDate(box.date, DATE_FORMAT_YYYYMMDD) : null,
        trackingNo: box.trackingNo,
        usNo: box.usNo,
        packer: box.packer,
        // Explicitly convert printLabel to a boolean value
        printLabel: box.printLabel === 'true' ? true : false,
        listShipCharg: Number(box.listShipCharg) || 0,
        actualShipCharg: Number(box.actualShipCharg) || 0,
      }));

      await saveShippingInfoBox({
        orderId: id,
        body: formattedBoxes ?? {
          orderId: getInfoAfterItemRowSelected()[0]?.orderId,
        },
      }).unwrap();
    } catch (error) {
      return error;
    }
  }, [boxForm, id, saveShippingInfoBox, getInfoAfterItemRowSelected]);

  useEffect(() => {
    const selectedTypes = getInfoAfterRowSelected();

    if (selectedTypes?.length) {
      const shipping = selectedTypes.some(
        (ele) => ele?.boxType === BoxType.SHIPPING
      );
      const returning = selectedTypes.some(
        (ele) => ele?.boxType === BoxType.RETURN
      );

      setTrackEquipmentStatus({
        shipping: shipping && !returning,
        return: returning && !shipping,
      });
    }

    if (itemFields?.length) {
      const totalMissing = itemFields.reduce(
        (total, item) => total + (item?.qtyTobeReturned || 0),
        0
      );
      setMeDisale(Number(totalMissing) === 0);
    }
  }, [getInfoAfterRowSelected, itemFields]);

  const hasSelectedBoxes = Object.keys(selectedBoxRows).length > 0;

  const isShippingEnabled = hasSelectedBoxes && trackEquipmentStatus.shipping;
  const isReturnEnabled = hasSelectedBoxes && trackEquipmentStatus.return;

  // Use a more specific form field watch instead of watching all rows
  useEffect(() => {
    const selectedRows = getInfoAfterItemRowSelected();
    if (!selectedRows?.length) return;

    const formValues = infoForm.getValues();
    const itemRows = formValues.info || [];

    const firstItemId = selectedRows[0]?.orderItemId;
    if (!firstItemId) return;

    const relatedRows = itemRows?.filter(
      (row) => row?.orderItemId == firstItemId
    );

    if (!relatedRows?.length) return;

    const originalOrder = relatedRows.find((row) => row?.splitNo == 0);
    if (!originalOrder) return;

    const orderQty = Number(originalOrder?.qtyOrdered || 0);
    const rowIndex = itemFields.findIndex(
      (item) => item.orderItemId == firstItemId && item.splitNo == 0
    );

    if (rowIndex === -1) return;

    const sumShipQty = relatedRows.reduce(
      (total, row) => total + Number(row?.qtyShipped || 0),
      0
    );

    const sumReturnQty = relatedRows.reduce(
      (total, row) => total + Number(row?.qtyReturn || 0),
      0
    );

    const isValidShip = sumShipQty >= 0 && sumShipQty <= orderQty;
    const isValidReturn = sumReturnQty >= 0 && sumReturnQty <= orderQty;

    setQuantityValidation({
      isValidShip,
      isValidReturn,
    });
  }, [getInfoAfterItemRowSelected, selectedItemRows, itemFields, infoForm]);

  return (
    <div className="flex flex-col gap-2 h-[500px] 2xl:h-[780px]">
      {/* Header with action buttons and status selection */}
      <div className="flex w-full justify-between">
        <div className="flex gap-2">
          {/* Action Buttons */}
          <AppButton
            icon={File}
            label=""
            tooltip="Print"
            className={cn(
              'bg-brand-teal-Default hover:bg-brand-teal-secondary rounded-[50%] w-10 h-10'
            )}
            onClick={() => setIsBoxDialogOpen((prev) => !prev)}
          />
          <AppButton
            icon={Box}
            label=""
            tooltip="Add Shipping Box"
            className={cn(
              'bg-brand-teal-Default hover:bg-brand-teal-secondary rounded-[50%] w-10 h-10',
              isLoadingAddShippingBox && 'bg-slate-300'
            )}
            onClick={() => addNewRow(SHIP_INFO_TYPE.SHIPPING)}
            isLoading={isLoadingAddShippingBox}
          />
          <AppButton
            icon={Package}
            label=""
            tooltip="Add Return Box"
            className={cn(
              'bg-brand-teal-Default hover:bg-brand-teal-secondary rounded-[50%] w-10 h-10',
              isLoadingAddReturnBox && 'bg-slate-300'
            )}
            onClick={() => addNewRow(SHIP_INFO_TYPE.RETURN)}
            isLoading={isLoadingAddReturnBox}
          />
          <AppButton
            icon={Earth}
            label=""
            tooltip="Track All Shipments"
            className={cn(
              'bg-brand-teal-Default hover:bg-brand-teal-secondary rounded-[50%] w-10 h-10',
              isLoadingTrackShipment && 'bg-slate-300'
            )}
            disabled={!isShippingEnabled}
            onClick={() => trackShipment('shipment')}
            isLoading={isLoadingTrackShipment}
          />
          <AppButton
            icon={EarthLock}
            label=""
            tooltip="Track All Returns"
            className={cn(
              ' bg-brand-teal-Default hover:bg-brand-teal-secondary rounded-[50%] w-10 h-10',
              isLoadingTrackReturn && 'bg-slate-300'
            )}
            disabled={!isReturnEnabled}
            onClick={() => trackShipment('return')}
            isLoading={isLoadingTrackReturn}
          />
          <AppButton
            icon={File}
            label=""
            tooltip="Create M/E"
            className={cn(
              'bg-brand-teal-Default hover:bg-brand-teal-secondary rounded-[50%] w-10 h-10',
              missingequLoading && 'bg-slate-300'
            )}
            onClick={onOpenChange}
            isLoading={missingequLoading}
            disabled={meDisale || isCreatingME}
          />
        </div>
        <div className="flex">
          <Labels label="Ship/Return Status" className="w-[150px]" />
          <SelectWidget
            form={infoForm}
            optionsList={statusList ?? []}
            name="shipStatus"
            placeholder="Select status"
            menuPosition="absolute"
            className="z-[1000] w-48"
            isClearable={false}
            onChange={handleStatusChange}
          />
        </div>
      </div>

      {/* Tables */}
      <div
        className={cn(
          'flex flex-col gap-2',
          shippingManager
            ? 'h-[420px] 2xl:h-[700px]'
            : 'h-[420px] 2xl:h-[700px]'
        )}
      >
        {' '}
        {/* First Table - Items Table (only shows when shippingManager exists) */}
        {shippingManager && (
          <div className="border shadow-md rounded-sm flex flex-col h-[320px] 2xl:h-[380px]">
            <div className="flex justify-end p-2 flex-shrink-0">
              <AppButton
                label="Save Items"
                icon={Save}
                onClick={infoForm.handleSubmit(handleSaveItem)}
                isLoading={isLoadingShippingInfoItem}
                disabled={
                  infoForm.getValues('info')?.length === 0 || isCreatingME
                }
              />
            </div>

            <div className="flex-1 ">
              <DataTable
                onScrollRef={onScrollRefItemData}
                data={itemFields ?? []}
                columns={columnsDetailsInfo ?? []}
                enablePagination={false}
                rowSelection={selectedItemRows}
                onRowSelectionChange={setSelectedItemRows}
                tableClassName="max-h-[250px] 2xl:max-h-[320px] overflow-y-auto"
                bindingKey="id"
                isLoading={isLoaderShippingInfoItem}
              />
            </div>
          </div>
        )}
        {/* Second Table - Boxes Table (takes full height when shippingManager is false) */}
        <div
          className={cn(
            'border shadow-md rounded-sm flex flex-col flex-1',
            shippingManager
              ? 'h-[320px] 2xl:h-[380px]'
              : 'h-[390px] 2xl:h-[670px]'
          )}
        >
          <div className="flex justify-end p-2 flex-shrink-0">
            <AppButton
              icon={Save}
              label="Save Boxes"
              onClick={boxForm.handleSubmit(handleSaveBox)}
              isLoading={isLoadingShippingInfoBox}
              disabled={boxForm.getValues('box')?.length === 0 || isCreatingME}
            />
          </div>

          <div className="flex-1 min-h-0">
            <DataTable
              onScrollRef={onScrollRefBoxData}
              data={boxFields as any}
              columns={columnsDetailsBox ?? []}
              enablePagination={false}
              enableRowSelection
              rowSelection={selectedBoxRows}
              onRowSelectionChange={setSelectedBoxRows}
              tableClassName={cn(
                'overflow-y-auto',
                shippingManager
                  ? 'max-h-[250px] 2xl:max-h-[280px]'
                  : 'max-h-[370px] 2xl:max-h-[650px]'
              )}
              bindingKey="id"
              isLoading={isLoaderShippingInfoBox}
            />
          </div>
        </div>
      </div>

      {/* Footer */}
      {/* Missing Equipment */}
      <MissingEquipmentDialog
        open={openMeDialog}
        onOpenChange={onOpenChange}
        isLoading={missingequLoading}
        handleSubmit={handleCreateMissingOrder}
      />

      {/* Future Invoice Warning */}
      <FutureInvoiceWarning
        open={showFutureInvoiceWarning}
        onClose={() => setShowFutureInvoiceWarning(false)}
      />

      {/* Tracking Warning */}
      <TrackingWarningDialog
        open={openTrackDialog}
        onClose={onOpenTrackChange}
      />
      {/* Print */}
      <PrintBoxDialog
        open={isBoxDialogOpen}
        onClose={() => setIsBoxDialogOpen((prev) => !prev)}
        shippingBoxList={shippingBoxList}
      />
    </div>
  );
};

export default Shipping;
