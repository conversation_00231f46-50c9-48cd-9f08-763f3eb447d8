import AppConfirmationModal from '@/components/common/app-confirmation-modal';

const TrackingWarningDialog = ({
  open,
  onClose,
}: {
  open: boolean;
  onClose: () => void;
}) => (
  <AppConfirmationModal
    title="Warning"
    description={
      <div>
        Please enter and save the tracking number before attempting to track the
        shipment.
      </div>
    }
    open={open}
    cancelLabel="OK"
    onOpenChange={onClose}
    handleCancel={onClose}
    aria-labelledby="track-confirmation-modal"
  />
);

export default TrackingWarningDialog;
